#!/usr/bin/env python3
"""
<PERSON>ript to create consolidated CSV files for visualization from federated learning data.
Creates two main files:
1. centralized_results.csv - Centralized training results
2. federated_results.csv - Federated training results with statistics
"""

import csv
import os
import glob
from collections import defaultdict
import statistics

def process_centralized_data():
    """Process centralized training data for both BART-Large and DistilBART"""
    print("Processing centralized data...")

    base_path = "central/20news"

    # File paths
    bart_large_file = f"{base_path}/training_metrics_centralized_bart_large_20news.csv"
    distilbart_file = f"{base_path}/training_metrics_centralized_distilbart_20news.csv.csv"

    centralized_data = []

    # Process BART-Large
    if os.path.exists(bart_large_file):
        with open(bart_large_file, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                row['model'] = 'BART-Large'
                row['training_type'] = 'Centralized'
                row['num_clients'] = '1'
                row['run_id'] = 'centralized'
                row['timestamp'] = row.get('source_file', 'unknown')
                row['f1_score'] = row.get('f1', '')
                centralized_data.append(row)
        print(f"Processed BART-Large: {len([r for r in centralized_data if r['model'] == 'BART-Large'])} records")

    # Process DistilBART
    if os.path.exists(distilbart_file):
        with open(distilbart_file, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                row['model'] = 'DistilBART'
                row['training_type'] = 'Centralized'
                row['num_clients'] = '1'
                row['run_id'] = 'centralized'
                row['timestamp'] = row.get('source_file', 'unknown')
                row['f1_score'] = row.get('f1', '')
                centralized_data.append(row)
        print(f"Processed DistilBART: {len([r for r in centralized_data if r['model'] == 'DistilBART'])} records")

    if centralized_data:
        # Save to CSV
        output_file = "centralized_results.csv"

        # Define final columns
        final_columns = [
            'model', 'training_type', 'num_clients', 'run_id', 'timestamp',
            'round', 'client_id', 'epoch', 'phase',
            'loss', 'accuracy', 'precision', 'recall', 'f1_score'
        ]

        with open(output_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=final_columns)
            writer.writeheader()

            for row in centralized_data:
                # Create clean row with only needed columns
                clean_row = {}
                for col in final_columns:
                    clean_row[col] = row.get(col, '')
                writer.writerow(clean_row)

        print(f"Saved centralized results to {output_file}: {len(centralized_data)} records")
        return centralized_data
    else:
        print("No centralized data found!")
        return []

def process_federated_data():
    """Process federated training data across all client configurations"""
    print("Processing federated data...")

    base_path = "federated/result_2_clients_to_10_clients_22rounds"

    federated_data = []

    # Process both BART-Large and DistilBART
    for model_name in ['bart_large', 'distilbart']:
        model_dir = f"{base_path}/results_{model_name}_fed_runs_20news"

        if not os.path.exists(model_dir):
            print(f"Directory not found: {model_dir}")
            continue

        print(f"Processing {model_name}...")

        # Process each client configuration (2-10 clients)
        for num_clients in range(2, 11):
            client_dir = f"{model_dir}/clients_{num_clients}_rounds_22"

            if not os.path.exists(client_dir):
                print(f"  Client dir not found: {client_dir}")
                continue

            print(f"  Processing {num_clients} clients...")

            # Process each run (different timestamps)
            run_dirs = glob.glob(f"{client_dir}/*/")

            for run_dir in run_dirs:
                timestamp = os.path.basename(run_dir.rstrip('/'))
                metrics_file = f"{run_dir}/training_metrics_{timestamp}.csv"

                if os.path.exists(metrics_file):
                    try:
                        with open(metrics_file, 'r') as f:
                            reader = csv.DictReader(f)
                            for row in reader:
                                row['model'] = 'BART-Large' if model_name == 'bart_large' else 'DistilBART'
                                row['training_type'] = 'Federated'
                                row['num_clients'] = str(num_clients)
                                row['run_id'] = timestamp
                                row['timestamp'] = timestamp
                                row['f1_score'] = row.get('f1', '')
                                federated_data.append(row)

                        print(f"    Added run {timestamp}")

                    except Exception as e:
                        print(f"    Error processing {metrics_file}: {e}")

    if federated_data:
        # Save raw federated data
        output_file = "federated_results_raw.csv"

        # Define final columns
        final_columns = [
            'model', 'training_type', 'num_clients', 'run_id', 'timestamp',
            'round', 'client_id', 'epoch', 'phase',
            'loss', 'accuracy', 'precision', 'recall', 'f1_score'
        ]

        with open(output_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=final_columns)
            writer.writeheader()

            for row in federated_data:
                # Create clean row with only needed columns
                clean_row = {}
                for col in final_columns:
                    clean_row[col] = row.get(col, '')
                writer.writerow(clean_row)

        print(f"Saved raw federated results to {output_file}: {len(federated_data)} records")

        # Create aggregated statistics
        federated_stats = create_federated_statistics(federated_data)

        return federated_data, federated_stats
    else:
        print("No federated data found!")
        return [], []

def create_federated_statistics(federated_data):
    """Create statistical summary of federated results"""
    print("Creating federated statistics...")

    # Filter for validation results (global evaluation)
    validation_data = [row for row in federated_data if row.get('client_id') == '-1']

    if not validation_data:
        print("No validation data found!")
        return []

    # Group by model, num_clients, and round to get statistics across runs
    groups = defaultdict(list)

    for row in validation_data:
        key = (row['model'], row['num_clients'], row['round'])
        groups[key].append(row)

    stats_list = []

    for (model, num_clients, round_num), group in groups.items():
        # Extract numeric values
        losses = [float(row['loss']) for row in group if row['loss']]
        accuracies = [float(row['accuracy']) for row in group if row['accuracy']]
        f1_scores = [float(row['f1_score']) for row in group if row['f1_score']]
        precisions = [float(row['precision']) for row in group if row['precision']]
        recalls = [float(row['recall']) for row in group if row['recall']]

        stats = {
            'model': model,
            'training_type': 'Federated',
            'num_clients': num_clients,
            'round': round_num,
            'num_runs': len(group),
        }

        # Add statistics if we have data
        if losses:
            stats.update({
                'loss_mean': statistics.mean(losses),
                'loss_std': statistics.stdev(losses) if len(losses) > 1 else 0,
                'loss_min': min(losses),
                'loss_max': max(losses),
            })

        if accuracies:
            stats.update({
                'accuracy_mean': statistics.mean(accuracies),
                'accuracy_std': statistics.stdev(accuracies) if len(accuracies) > 1 else 0,
                'accuracy_min': min(accuracies),
                'accuracy_max': max(accuracies),
            })

        if f1_scores:
            stats.update({
                'f1_mean': statistics.mean(f1_scores),
                'f1_std': statistics.stdev(f1_scores) if len(f1_scores) > 1 else 0,
                'f1_min': min(f1_scores),
                'f1_max': max(f1_scores),
            })

        if precisions:
            stats.update({
                'precision_mean': statistics.mean(precisions),
                'precision_std': statistics.stdev(precisions) if len(precisions) > 1 else 0,
            })

        if recalls:
            stats.update({
                'recall_mean': statistics.mean(recalls),
                'recall_std': statistics.stdev(recalls) if len(recalls) > 1 else 0,
            })

        stats_list.append(stats)

    # Save statistics
    output_file = "federated_results_stats.csv"

    if stats_list:
        # Get all possible fieldnames
        all_fields = set()
        for stats in stats_list:
            all_fields.update(stats.keys())

        fieldnames = sorted(all_fields)

        with open(output_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(stats_list)

        print(f"Saved federated statistics to {output_file}: {len(stats_list)} records")

    return stats_list

def main():
    """Main function to process all data"""
    print("Starting data consolidation...")

    # We're already in the correct directory

    # Process centralized data
    centralized_data = process_centralized_data()

    # Process federated data
    federated_data, federated_stats = process_federated_data()

    # Create a combined summary
    print("\n=== SUMMARY ===")
    print(f"Centralized records: {len(centralized_data)}")
    print(f"Federated records: {len(federated_data)}")
    print(f"Federated statistics: {len(federated_stats)}")

    if centralized_data:
        models = set(row['model'] for row in centralized_data)
        phases = set(row['phase'] for row in centralized_data)
        print(f"\nCentralized models: {models}")
        print(f"Centralized phases: {phases}")

    if federated_data:
        models = set(row['model'] for row in federated_data)
        clients = set(row['num_clients'] for row in federated_data)
        phases = set(row['phase'] for row in federated_data)
        print(f"\nFederated models: {models}")
        print(f"Client configurations: {sorted(clients)}")
        print(f"Federated phases: {phases}")

    print("\nFiles created:")
    print("- centralized_results.csv")
    print("- federated_results_raw.csv")
    print("- federated_results_stats.csv")

    print("\nData consolidation complete!")

if __name__ == "__main__":
    main()
